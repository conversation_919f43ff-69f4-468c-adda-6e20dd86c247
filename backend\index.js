const express = require('express');
const cors = require('cors');
const { downloadPlaylist } = require('./downloader');
const path = require('path');

const app = express();
app.use(cors());
app.use(express.json());

// Serve static files dari folder zips
app.use('/zips', express.static(path.join(__dirname, '../zips')));

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'OK', message: 'YouTube Playlist Downloader API is running' });
});

app.post('/download', async (req, res) => {
  const { url, format } = req.body;

  // Validasi input
  if (!url || !format) {
    return res.status(400).json({
      error: 'Missing required fields',
      message: 'URL dan format harus diisi'
    });
  }

  if (typeof url !== 'string' || typeof format !== 'string') {
    return res.status(400).json({
      error: 'Invalid input type',
      message: 'URL dan format harus berupa string'
    });
  }

  console.log(`Received download request - URL: ${url}, Format: ${format}`);

  try {
    const zipPath = await downloadPlaylist(url, format);
    console.log(`Download completed successfully: ${zipPath}`);

    res.json({
      status: 'success',
      message: 'Download selesai',
      downloadLink: zipPath,
      timestamp: new Date().toISOString()
    });
  } catch (err) {
    console.error('Download error:', err.message);
    res.status(500).json({
      error: 'Download failed',
      message: err.message
    });
  }
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Unhandled error:', err);
  res.status(500).json({
    error: 'Internal server error',
    message: 'Terjadi kesalahan pada server'
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    error: 'Not found',
    message: 'Endpoint tidak ditemukan'
  });
});

const PORT = process.env.PORT || 5000;
app.listen(PORT, () => {
  console.log(`Backend running on http://localhost:${PORT}`);
  console.log('Available endpoints:');
  console.log('  GET  /health - Health check');
  console.log('  POST /download - Download playlist');
  console.log('  GET  /zips/* - Serve downloaded files');
});
