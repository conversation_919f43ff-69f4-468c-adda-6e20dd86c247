const { exec } = require('child_process');
const path = require('path');
const fs = require('fs');
const AdmZip = require('adm-zip');

function downloadPlaylist(url, format) {
  return new Promise((resolve, reject) => {
    const projectRoot = path.resolve(__dirname, '..');
    const downloadDir = path.join(projectRoot, 'downloads');
    const zipDir = path.join(projectRoot, 'zips');
    const timestamp = Date.now();
    const zipFile = path.join(zipDir, `playlist-${timestamp}.zip`);

    // Pastikan folder downloads dan zips ada
    if (!fs.existsSync(downloadDir)) {
      fs.mkdirSync(downloadDir, { recursive: true });
    }
    if (!fs.existsSync(zipDir)) {
      fs.mkdirSync(zipDir, { recursive: true });
    }

    // Validasi URL YouTube
    if (!url.includes('youtube.com') && !url.includes('youtu.be')) {
      reject(new Error('URL harus dari YouTube'));
      return;
    }

    // Validasi format
    if (!['mp3', 'mp4'].includes(format)) {
      reject(new Error('Format harus mp3 atau mp4'));
      return;
    }

    console.log(`Memulai download playlist: ${url} dengan format: ${format}`);

    let ytdlpCommand = `python -m yt_dlp -o "downloads/%(title)s.%(ext)s" `;

    if (format === 'mp3') {
      ytdlpCommand += `-f bestaudio --extract-audio --audio-format mp3 --audio-quality 0 "${url}"`;
    } else {
      ytdlpCommand += `-f best "${url}"`;
    }

    console.log(`Menjalankan command: ${ytdlpCommand}`);

    exec(ytdlpCommand, { cwd: projectRoot }, (err, stdout, stderr) => {
      if (err) {
        console.error('Error saat download:', stderr || err.message);
        reject(new Error(stderr || err.message));
      } else {
        console.log('Download selesai:', stdout);

        try {
          // Buat zip dari folder downloads
          const zip = new AdmZip();
          const files = fs.readdirSync(downloadDir);

          if (files.length === 0) {
            reject(new Error('Tidak ada file yang berhasil didownload'));
            return;
          }

          files.forEach(file => {
            const filePath = path.join(downloadDir, file);
            if (fs.statSync(filePath).isFile()) {
              zip.addLocalFile(filePath);
            }
          });

          zip.writeZip(zipFile);
          console.log(`ZIP file dibuat: ${zipFile}`);

          // Kosongin folder downloads biar nggak numpuk
          files.forEach(file => {
            const filePath = path.join(downloadDir, file);
            if (fs.statSync(filePath).isFile()) {
              fs.unlinkSync(filePath);
            }
          });

          resolve(`/zips/playlist-${timestamp}.zip`);
        } catch (zipError) {
          console.error('Error saat membuat ZIP:', zipError);
          reject(new Error('Gagal membuat file ZIP'));
        }
      }
    });
  });
}

module.exports = { downloadPlaylist };
