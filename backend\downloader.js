const { exec } = require('child_process');
const path = require('path');
const fs = require('fs');
const AdmZip = require('adm-zip');

function downloadPlaylist(url, format) {
  return new Promise((resolve, reject) => {
    const projectRoot = path.resolve(__dirname, '..');
    const downloadDir = path.join(projectRoot, 'downloads');
    const zipDir = path.join(projectRoot, 'zips');
    const timestamp = Date.now();
    const zipFile = path.join(zipDir, `playlist-${timestamp}.zip`);

    // Pastikan folder downloads dan zips ada
    if (!fs.existsSync(downloadDir)) {
      fs.mkdirSync(downloadDir, { recursive: true });
    }
    if (!fs.existsSync(zipDir)) {
      fs.mkdirSync(zipDir, { recursive: true });
    }

    // Validasi URL YouTube
    if (!url.includes('youtube.com') && !url.includes('youtu.be')) {
      reject(new Error('URL harus dari YouTube'));
      return;
    }

    // Validasi format
    if (!['mp3', 'mp4'].includes(format)) {
      reject(new Error('Format harus mp3 atau mp4'));
      return;
    }

    console.log(`Memulai download playlist: ${url} dengan format: ${format}`);

    let ytdlpCommand = `python -m yt_dlp -o "downloads/%(title)s.%(ext)s" `;

    if (format === 'mp3') {
      // Download audio saja tanpa konversi MP3 (karena FFmpeg belum tersedia)
      ytdlpCommand += `-f bestaudio "${url}"`;
    } else {
      ytdlpCommand += `-f best "${url}"`;
    }

    console.log(`Menjalankan command: ${ytdlpCommand}`);

    // Timeout untuk proses download (15 menit)
    const timeout = 15 * 60 * 1000; // 15 menit
    let processKilled = false;

    const childProcess = exec(ytdlpCommand, { cwd: projectRoot }, (err, stdout, stderr) => {
      if (processKilled) {
        reject(new Error('Download timeout - proses terlalu lama'));
        return;
      }

      // Cek apakah ada file yang berhasil didownload meskipun ada error
      const files = fs.readdirSync(downloadDir).filter(file =>
        fs.statSync(path.join(downloadDir, file)).isFile()
      );

      if (files.length === 0) {
        console.error('Error saat download:', stderr || err?.message || 'Tidak ada file yang berhasil didownload');
        reject(new Error('Tidak ada file yang berhasil didownload. Pastikan URL playlist valid dan koneksi internet stabil.'));
        return;
      }

      console.log(`Download selesai. ${files.length} file berhasil didownload.`);
      console.log('Files:', files.map(f => f.substring(0, 50) + '...').join(', '));

      try {
        // Buat zip dari folder downloads
        const zip = new AdmZip();

        files.forEach(file => {
          const filePath = path.join(downloadDir, file);
          zip.addLocalFile(filePath);
        });

        zip.writeZip(zipFile);
        console.log(`ZIP file dibuat: ${zipFile}`);

        // Kosongin folder downloads biar nggak numpuk
        files.forEach(file => {
          const filePath = path.join(downloadDir, file);
          fs.unlinkSync(filePath);
        });

        const formatInfo = format === 'mp3' ? 'Audio (WebM format - dapat diputar di semua pemutar modern)' : 'Video (MP4)';
        resolve({
          downloadLink: `/zips/playlist-${timestamp}.zip`,
          fileCount: files.length,
          format: formatInfo
        });
      } catch (zipError) {
        console.error('Error saat membuat ZIP:', zipError);
        reject(new Error('Gagal membuat file ZIP'));
      }
    });

    // Set timeout
    const timeoutId = setTimeout(() => {
      processKilled = true;
      childProcess.kill();
      console.log('Download timeout - membunuh proses');
    }, timeout);

    // Clear timeout jika proses selesai normal
    childProcess.on('exit', () => {
      clearTimeout(timeoutId);
    });
  });
}

module.exports = { downloadPlaylist };
